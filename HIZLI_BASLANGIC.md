# ⚡ Base64 Streaming MCP - <PERSON><PERSON><PERSON><PERSON><PERSON> Başlangıç

5 dakikada Claude Desktop ile ekran paylaşımını aktif hale getirin!

## 🚀 <PERSON><PERSON><PERSON><PERSON><PERSON> (5 Dakika)

### 1️⃣ <PERSON> (30 saniye)
```bash
python --version
# Python 3.7+ olmalı. Yoksa python.org'dan indirin.
```

### 2️⃣ MCP Server Kurulumu (2 dakika)
```bash
pip install git+https://github.com/inkbytefo/base64-streaming-mcp.git
```

### 3️⃣ Test (30 saniye)
```bash
base64-streaming-mcp --help
```

### 4️⃣ Claude Desktop Konfigürasyonu (2 dakika)

#### Windows:
1. `%APPDATA%\Claude\claude_desktop_config.json` dosyasını açın
2. Şu içeriği ekleyin:
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--host", "localhost", "--port", "8080"]
    }
  }
}
```

#### macOS:
1. `~/Library/Application Support/Claude/claude_desktop_config.json` dosyasını açın
2. Şu içeriği ekleyin:
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--host", "localhost", "--port", "8080"]
    }
  }
}
```

#### Linux:
1. `~/.config/Claude/claude_desktop_config.json` dosyasını açın
2. Şu içeriği ekleyin:
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--host", "localhost", "--port", "8080"]
    }
  }
}
```

### 5️⃣ Claude Desktop'ı Yeniden Başlatın (30 saniye)

## ✅ Test Edin

Claude Desktop'ta şunu yazın:
```
"Ekranımı yakalayabilir misin?"
```

## 🎯 Yaygın Kullanım Senaryoları

### 📊 Sunum Paylaşımı
```
"Ekranımı 30 FPS ile paylaş"
"Sunum modunda ekran yakala"
"Yüksek kalitede ekran akışı başlat"
```

### 💻 Kod İncelemesi
```
"Kod editörümü yakala"
"Terminal ekranını göster"
"IDE'deki hatayı yakala"
```

### 🎮 Oyun/Uygulama Gösterimi
```
"Oyun ekranını kaydet"
"Uygulama arayüzünü yakala"
"Animasyonu kaydet"
```

### 📱 Mobil Geliştirme
```
"Emülatör ekranını yakala"
"Mobil uygulama testini göster"
"Responsive tasarımı kontrol et"
```

## ⚙️ Hızlı Ayarlar

### Düşük Bant Genişliği
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--fps", "15", "--quality", "70"]
    }
  }
}
```

### Yüksek Kalite
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--fps", "60", "--quality", "95"]
    }
  }
}
```

### Genel Kullanım
```json
{
  "mcpServers": {
    "base64-streaming-mcp": {
      "command": "base64-streaming-mcp",
      "args": ["--fps", "30", "--quality", "85"]
    }
  }
}
```

## 🔧 Sorun mu Var?

### ❌ "Command not found"
```bash
# Komutun yolunu bulun
which base64-streaming-mcp

# Tam yolu konfigürasyonda kullanın
```

### ❌ Claude Desktop bağlanmıyor
1. Claude Desktop'ı tamamen kapatın
2. JSON formatını kontrol edin
3. Yeniden başlatın

### ❌ İzin hatası (macOS)
1. System Preferences > Security & Privacy > Privacy > Screen Recording
2. Claude Desktop'ı listeye ekleyin

### ❌ Port çakışması
```json
{
  "args": ["--port", "8081"]
}
```

## 📞 Yardım

- 📖 Detaylı rehber: `KULLANICI_KURULUM_REHBERI.md`
- 🤖 Claude entegrasyonu: `CLAUDE_DESKTOP_ENTEGRASYONU.md`
- 🐛 Sorun bildirimi: GitHub Issues
- 💬 Topluluk: GitHub Discussions

## 🎉 Başarılı Kurulum Sonrası

Claude Desktop ile şunları deneyebilirsiniz:

### Temel Komutlar
- "Ekranımı yakala"
- "Monitörleri listele"
- "Sunucu durumu"
- "Akışı başlat/durdur"

### Gelişmiş Özellikler
- "Belirli bir bölgeyi yakala"
- "Kaliteyi artır/azalt"
- "FPS'i değiştir"
- "Sıkıştırma ayarla"

### Otomatik Görevler
- "Her 5 saniyede ekran yakala"
- "Değişiklikleri izle"
- "Performans raporla"

## 🚀 Sonraki Adımlar

1. **Performans Optimizasyonu**: Sisteminize göre FPS ve kalite ayarlayın
2. **Güvenlik**: Gerekirse firewall kuralları ekleyin
3. **Otomasyon**: Kendi komutlarınızı oluşturun
4. **İzleme**: Log dosyalarını kontrol edin

---

**🎯 Hedef**: 5 dakikada çalışır duruma getirmek
**✅ Sonuç**: Claude Desktop ile ekran paylaşımı aktif!

Herhangi bir sorunla karşılaştığınızda, detaylı rehberlere bakın veya GitHub'da issue açın.
